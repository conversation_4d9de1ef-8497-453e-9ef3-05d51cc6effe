"use client";

import OnlyofficeRender from "@/src/components/onlyoffice-render";
import { ReportModelInterface } from "@/src/types/core/report-model";
import { LogOut, ListTree } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState, useRef } from "react";
import { loadFileEditorById } from "@/src/actions/file-editor";
import { TemplateVariablesPopup } from "@/src/components/template-variables-popup";

type DocumentEditorPageProps = {
  params: {
    id: string;
  };
};

const getFileType = (mimetype: string) => {
  const types: { [key: string]: string } = {
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document": "docx",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": "xlsx",
    "application/vnd.openxmlformats-officedocument.presentationml.presentation": "pptx",
    "application/pdf": "pdf",
  };
  return types[mimetype] || "docx";
};

void getFileType;

const getDocumentType = (mimetype: string): "word" | "cell" | "slide" => {
  const types: { [key: string]: "word" | "cell" | "slide" } = {
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document": "word",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": "cell",
    "application/vnd.openxmlformats-officedocument.presentationml.presentation": "slide",
    "application/pdf": "word",
  };
  return types[mimetype] || "word";
};


export default function DocumentEditorPage({ params }: DocumentEditorPageProps) {
  const [loading, setLoading] = useState(true);
  const router = useRouter();
  const [fileData, setFileData] = useState<ReportModelInterface>();
  const [showVariablesPopup, setShowVariablesPopup] = useState(false);
  const editorRef = useRef<any>(null);

  // Estados para controlar a exibição dos botões
  const [fromProposalTemplates, setFromProposalTemplates] = useState(false);
  const [fromCustomerProjectsHistory, setFromCustomerProjectsHistory] = useState(false);
  const [fromProposalForm, setFromProposalForm] = useState(false);
  const [referrerUrl, setReferrerUrl] = useState<string | null>(null);

  useEffect(() => {
    // Verificar se há um referrer armazenado na sessionStorage
    const referrer = sessionStorage.getItem('documentEditorReferrer');

    // Verificar a origem do referrer
    if (referrer) {
      // Armazenar a URL do referrer para navegação
      setReferrerUrl(referrer);

      // Verificar se veio da página de templates de proposta
      if (referrer.includes('/views/proposal-templates') && referrer.includes('type=PROPOSAL')) {
        setFromProposalTemplates(true);
      }

      // Verificar se veio da página de histórico de projetos do cliente
      if (referrer.includes('/views/crm/customer-projects-history')) {
        setFromCustomerProjectsHistory(true);
      }

      // Verificar se veio do formulário de proposta
      if (referrer.includes('/views/crm/proposals/')) {
        setFromProposalForm(true);
      }
    }

    // Limpar o referrer após verificação
    sessionStorage.removeItem('documentEditorReferrer');
  }, []);

  const fetchFileEditorFile = async () => {
    try {
      const data = await loadFileEditorById(params.id);
      if (data) setFileData(data);
    } catch (error) {
      console.error("Error loading file:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchFileEditorFile();
  }, [params.id]);

  // Função para lidar com variáveis (não usada mais, já que agora copiamos para a área de transferência)
  const handleInsertVariable = (variable: string) => {
    // Mantida apenas para compatibilidade com a interface
    console.log("Variável copiada:", variable);
  };

  return (
    <div className="w-full h-screen relative">
      {/* Container de botões - exibido apenas se não vier da página de histórico de projetos */}
      {!fromCustomerProjectsHistory && (
        <div className="fixed right-[5px] top-[2px] flex gap-1">
          {/* Botão de variáveis - exibido apenas se vier da rota de templates de proposta */}
          {fromProposalTemplates && (
            <div
              onClick={() => setShowVariablesPopup(!showVariablesPopup)}
              className="group flex flex-col items-center gap-0.5 px-6 py-1.5 rounded-lg shadow-lg bg-green-600 text-white cursor-pointer"
            >
              <ListTree className="h-5 w-5 transition-transform duration-200 hover:scale-110 group-hover:scale-110" />
              <span className="text-sm font-medium transition-all duration-200 hover:scale-110 group-hover:scale-110">Variáveis</span>
            </div>
          )}

          {/* Botão voltar */}
          <div
            onClick={() => {
              // Se temos uma URL de referrer específica, navegar para ela
              if (referrerUrl) {
                router.push(referrerUrl);
              } else {
                // Caso contrário, usar o comportamento padrão
                router.back();
              }
            }}
            className="group flex flex-col items-center gap-0.5 px-8 py-1.5 rounded-lg shadow-lg bg-primary text-primary-foreground cursor-pointer"
          >
            <LogOut className="h-5 w-5 transition-transform duration-200 hover:scale-110 group-hover:scale-110" />
            <span className="text-sm font-medium transition-all duration-200 hover:scale-110 group-hover:scale-110">Voltar</span>
          </div>
        </div>
      )}

      {/* Popup de variáveis - exibido apenas se vier da rota de templates de proposta */}
      {fromProposalTemplates && (
        <TemplateVariablesPopup
          isOpen={showVariablesPopup}
          onClose={() => setShowVariablesPopup(false)}
          onInsertVariable={handleInsertVariable}
        />
      )}

      {loading ? (
        <div className="flex h-full items-center justify-center">
          Carregando documento...
        </div>
      ) : fileData ? (
        <OnlyofficeRender
          key={`editor-${fileData.version}`} // Força recriação ao alterar versão
          fileKey={fileData.key}
          documentType={getDocumentType(fileData.mimetype)}
          title={fileData.filename}
          mimetype={fileData.mimetype}
          editorRef={editorRef}
        />
      ) : (
        <div className="flex h-full items-center justify-center text-red-500">
          Documento não encontrado
        </div>
      )}
    </div>
  );
}